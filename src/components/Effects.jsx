import React, { useMemo, useRef } from 'react';
import { useImperativeHandle, forwardRef } from 'react';
import { EffectComposer, Bloom, Vignette } from '@react-three/postprocessing';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass';
import * as THREE from 'three';
import { gsap } from 'gsap';

// You will need to create these shader files
import vertexShader from '../three/shaders/vertex.glsl';
import fragmentShader from '../three/shaders/fragment.glsl';

const TransitionEffect = forwardRef((props, ref) => {
    const shaderPass = useMemo(() => {
        const textureLoader = new THREE.TextureLoader();
        const noiseTexture = textureLoader.load('/noise.png');
        noiseTexture.wrapS = THREE.RepeatWrapping;
        noiseTexture.wrapT = THREE.RepeatWrapping;
        
        const shader = {
            uniforms: new Map([
                ['tDiffuse', new THREE.Uniform(null)],
                ['tVideo', new THREE.Uniform(null)],
                ['tNoise', new THREE.Uniform(noiseTexture)],
                ['u_progress', new THREE.Uniform(0.0)],
                ['u_intensity', new THREE.Uniform(0.0)],
            ]),
            vertexShader,
            fragmentShader,
        };
        return new ShaderPass(shader, 'tDiffuse');
    }, []);

    useImperativeHandle(ref, () => ({
        startVideoTransition(videoSrc) {
            const video = document.createElement('video');
            video.src = videoSrc;
            video.muted = true;
            video.loop = false;
            video.playsInline = true;

            const videoTexture = new THREE.VideoTexture(video);
            shaderPass.uniforms.get('tVideo').value = videoTexture;

            const uniforms = shaderPass.uniforms;
            const tl = gsap.timeline();

            tl.to(uniforms.get('u_intensity'), { value: 0.3, duration: 0.7, ease: 'power2.in' })
              .to(uniforms.get('u_intensity'), { value: 0.0, duration: 0.7, ease: 'power2.out' });
            
            gsap.to(uniforms.get('u_progress'), {
                value: 1.0,
                duration: 1.4,
                ease: 'linear',
                onComplete: () => {
                    uniforms.get('u_progress').value = 0.0;
                }
            });

            video.play();
        }
    }));

    return <primitive object={shaderPass} />;
});


const Effects = forwardRef((props, ref) => {
  // You can add your other useControls for bloom/vignette here
  return (
    <EffectComposer>
      <Bloom intensity={0.2} luminanceThreshold={0.18} />
      <Vignette offset={0.19} darkness={0.7} />
      <TransitionEffect ref={ref} />
    </EffectComposer>
  );
});

export default Effects;