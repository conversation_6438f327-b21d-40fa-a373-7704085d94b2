uniform sampler2D tDiffuse;
uniform sampler2D tVideo;
uniform sampler2D tNoise;
uniform float u_progress;
uniform float u_intensity;

varying vec2 vUv;

void main() {
    vec4 noise = texture2D(tNoise, vUv);
    vec2 offset = vec2(noise.r - 0.5, noise.g - 0.5) * u_intensity;

    vec2 uvScene = vUv + offset;
    vec2 uvVideo = vUv + offset;

    vec4 sceneColor = texture2D(tDiffuse, uvScene);
    vec4 videoColor = texture2D(tVideo, uvVideo);

    // Fade quickly between the two textures around the halfway point
    float mixFactor = smoothstep(0.4, 0.6, u_progress);
    vec4 finalColor = mix(sceneColor, videoColor, mixFactor);

    gl_FragColor = finalColor;
}