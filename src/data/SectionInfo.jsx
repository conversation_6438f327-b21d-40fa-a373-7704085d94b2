const panelSectionsData = [
    {
      id: 'Section1',
      title: 'Crowd & Event Management',
      models: [
        { srNo: 'A-1', category: 'Crowd mgmt.', model: 'Crowd Density & Flow', description: 'Real-time people count + heat-maps', progress: '', availableVideos: '' },
        { srNo: 'A-2', category: 'Crowd mgmt.', model: 'Surge / Stampede Predictor', description: 'Detect stop-waves / reverse flow', progress: '', availableVideos: '' },
        { srNo: 'A-3', category: 'Crowd mgmt.', model: 'Queue Analytics', description: 'Avg. wait-time at gates', progress: '', availableVideos: '' },
        { srNo: 'A-4', category: 'Crowd mgmt.', model: 'Lost-/Missing-Person Re-ID', description: 'Cross-camera face re-identification', progress: '', availableVideos: '' },
        { srNo: 'A-5', category: 'Crowd mgmt.', model: 'Aggression / Fight Detector', description: 'Posture & gesture-based', progress: '', availableVideos: '' },
      ],
    },
    {
      id: 'Section2',
      title: 'Traffic Analytics',
      models: [
        { srNo: 'B-1', category: 'Traffic', model: 'Vehicle Count & Classification', description: 'Multi-class (car/bike/bus/truck)', progress: 'Done', availableVideos: '' },
        { srNo: 'B-2', category: 'Traffic', model: 'Red-Light Violation', description: 'RLVD with ANPR hand-off', progress: 'To Render', availableVideos: '' },
        { srNo: 'B-3', category: 'Traffic', model: 'Speed Violation', description: 'Radar-free vision speed-estimation', progress: 'Done', availableVideos: '' },
        { srNo: 'B-4', category: 'Traffic', model: 'Wrong-Way Driving', description: 'Direction vector analysis', progress: 'To Render', availableVideos: '' },
        { srNo: 'B-5', category: 'Traffic', model: 'Helmet-less Rider', description: 'Two-wheeler rider detection', progress: 'Editing', availableVideos: '' },
        { srNo: 'B-6', category: 'Traffic', model: 'Seat-belt Violation', description: 'Vision-based seat-belt check', progress: 'To Render', availableVideos: '' },
        { srNo: 'B-7', category: 'Traffic', model: 'Triple-Riding Detection', description: '>2 persons on bike', progress: 'Editing', availableVideos: '' },
        { srNo: 'B-8', category: 'Traffic', model: 'Automated Number-Plate Recognition (ANPR)', description: 'India-font OCR & e-challan', progress: 'Done', availableVideos: '' },
        { srNo: 'B-9', category: 'Traffic', model: 'Stalled / Incident Detection', description: 'Stopped vehicle, accident', progress: 'Done', availableVideos: '' },
        { srNo: 'B-10', category: 'Traffic', model: 'Weigh-in-Motion Overload', description: 'Vision + axle-count fusion', progress: 'To Render', availableVideos: '' },
        { srNo: 'B-11', category: 'Traffic', model: 'Smart-Parking Occupancy', description: 'Bay-level space detection', progress: 'Done', availableVideos: '/videos/parking_web.mp4' },
      ],
    },
    {
      id: 'Section3',
      title: 'Public Safety & Security',
      models: [
        { srNo: 'C-1', category: 'Security', model: 'Perimeter / Zone Intrusion', description: 'Rule-based line-cross etc.', progress: '', availableVideos: '' },
        { srNo: 'C-2', category: 'Security', model: 'Loitering Detection', description: 'Time-in-zone analytics', progress: '', availableVideos: '' },
        { srNo: 'C-3', category: 'Security', model: 'Abandoned / Suspect Object', description: 'Unattended bag alert', progress: '', availableVideos: '' },
        { srNo: 'C-4', category: 'Security', model: 'Object Removal / Theft', description: 'Protected asset taken-away', progress: '', availableVideos: '' },
        { srNo: 'C-5', category: 'Security', model: 'Weapon / Knife-Flash', description: 'Sudden metallic flash', progress: '', availableVideos: '' },
        { srNo: 'C-6', category: 'Security', model: 'Facial Watch-List Match', description: 'Court-approved hot-list', progress: '', availableVideos: '' },
        { srNo: 'C-7', category: 'Security', model: 'Gender-Safety Corridor Alert', description: 'Woman-alone late-night', progress: '', availableVideos: '' },
        { srNo: 'C-8', category: 'Security', model: 'Camera Tamper / Defocus', description: 'Obstruction, blur, tilt', progress: '', availableVideos: '' },
      ],
    },
    {
      id: 'Section4',
      title: 'Disaster & Emergency Response',
      models: [
        { srNo: 'D-1', category: 'Emergency', model: 'Fire & Smoke Detection', description: 'Day & night spectral model', progress: '', availableVideos: '' },
        { srNo: 'D-2', category: 'Emergency', model: 'Flooded-Road / Water-Log', description: 'Pixel depth estimation', progress: '', availableVideos: '' },
        { srNo: 'D-3', category: 'Emergency', model: 'Coastal Surge / Shore Rise', description: 'Wave-line anomaly', progress: '', availableVideos: '' },
        { srNo: 'D-4', category: 'Emergency', model: 'Accident Impact Severity', description: 'Rollover / heavy impact', progress: 'To edit', availableVideos: '' },
      ],
    },
    {
      id: 'Section5',
      title: 'Environmental & Civic Services',
      models: [
        { srNo: 'E-1', category: 'Civic', model: 'Garbage-Bin Fill-Level', description: '4-class fullness model', progress: '', availableVideos: '' },
        { srNo: 'E-2', category: 'Civic', model: 'Illegal Dumping / Littering', description: 'Plastic, debris detection', progress: '', availableVideos: '' },
        { srNo: 'E-3', category: 'Civic', model: 'Stray-Animal Detection', description: 'Cattle / dog on carriageway', progress: '', availableVideos: '' },
        { srNo: 'E-4', category: 'Civic', model: 'Pothole / Road-Damage', description: 'Texture & depth AI', progress: '', availableVideos: '' },
        { srNo: 'E-5', category: 'Civic', model: 'Street-Light Outage', description: 'Lamp-out recognition', progress: '', availableVideos: '' },
        { srNo: 'E-6', category: 'Civic', model: 'Air-Pollution Plume', description: 'Smoke/stack visual corroboration', progress: '', availableVideos: '' },
        { srNo: 'E-7', category: 'Civic', model: 'Heritage-Wall Graffiti', description: 'Vandalism on protected sites', progress: '', availableVideos: '' },
      ],
    },
    {
      id: 'Section6',
      title: 'Tourism & Operational Analytics',
      models: [
        { srNo: 'F-1', category: 'Tourism', model: 'Visitor Footfall & Dwell-Time', description: 'Heat-maps for sites', progress: '', availableVideos: '' },
        { srNo: 'F-2', category: 'Tourism', model: 'Demographic Analytics', description: 'Age-band / group-size', progress: '', availableVideos: '' },
        { srNo: 'F-3', category: 'Ops', model: 'Video Synopsis / Time-Lapse', description: 'Rapid review generator', progress: '', availableVideos: '' },
      ],
    }
  ];

  export default panelSectionsData;