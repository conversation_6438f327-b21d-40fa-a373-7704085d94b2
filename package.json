{"name": "iris-start", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-three/drei": "^10.5.1", "@react-three/fiber": "^9.2.0", "@react-three/postprocessing": "^3.0.4", "@tailwindcss/vite": "^4.1.11", "framer-motion": "^12.23.9", "gsap": "^3.13.0", "leva": "^0.10.0", "postprocessing": "^6.37.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11", "vite-plugin-glsl": "^1.5.1", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}}